# NeuroV CRM - Webhook Integration Fixes Test Script
# PowerShell 7+ Compatible
# 
# This script validates the critical webhook integration fixes:
# 1. Business Profile Webhook Logging Integration  
# 2. API Base URL Configuration Standardization
#
# Usage: .\test_webhook_fixes.ps1

Write-Host "🚀 NeuroV CRM - Webhook Integration Fixes Validation" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

# Configuration
$BackendUrl = "http://localhost:8000"
$FrontendUrl = "http://localhost:3000"
$WebhookEndpoint = "$BackendUrl/api/v1/business-profile/webhook"

# Test results storage
$TestResults = @()

function Add-TestResult {
    param(
        [string]$TestName,
        [string]$Status,
        [string]$Details
    )
    
    $TestResults += [PSCustomObject]@{
        Test = $TestName
        Status = $Status
        Details = $Details
        Timestamp = Get-Date
    }
}

function Test-BackendConnectivity {
    Write-Host "🔍 Testing Backend Connectivity..." -ForegroundColor Yellow
    
    try {
        $response = Invoke-RestMethod -Uri "$BackendUrl/docs" -Method Get -TimeoutSec 10
        Add-TestResult -TestName "Backend Connectivity" -Status "PASS" -Details "Backend server is accessible at $BackendUrl"
        return $true
    }
    catch {
        Add-TestResult -TestName "Backend Connectivity" -Status "FAIL" -Details "Backend server not accessible: $($_.Exception.Message)"
        return $false
    }
}

function Test-BusinessProfileWebhook {
    Write-Host "🔍 Testing Business Profile Webhook Integration..." -ForegroundColor Yellow
    
    # Create test webhook payload
    $testPayload = @{
        CustomerProfileSid = "BU1234567890abcdef1234567890abcdef"
        Status = "twilio-approved"
        EventType = "customer-profile.status.changed"
        Timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    } | ConvertTo-Json
    
    try {
        $headers = @{
            "Content-Type" = "application/json"
            "X-Twilio-Signature" = "test-signature"
        }
        
        $response = Invoke-RestMethod -Uri $WebhookEndpoint -Method Post -Body $testPayload -Headers $headers -TimeoutSec 30
        
        if ($response.webhook_id) {
            Add-TestResult -TestName "Business Profile Webhook Processing" -Status "PASS" -Details "Webhook processed successfully. Webhook ID: $($response.webhook_id)"

            # Test webhook logging integration
            Add-TestResult -TestName "Webhook Logging Integration" -Status "PASS" -Details "Webhook ID generated indicates proper logging integration"
        }
        else {
            Add-TestResult -TestName "Business Profile Webhook Processing" -Status "WARNING" -Details "Webhook processed but no webhook_id returned"
        }
    }
    catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        $errorMessage = $_.Exception.Message
        Add-TestResult -TestName "Business Profile Webhook Processing" -Status "FAIL" -Details "HTTP $($statusCode): $errorMessage"
    }
}

function Test-APIConfigurationStandardization {
    Write-Host "🔍 Testing API Configuration Standardization..." -ForegroundColor Yellow
    
    $endpoints = @(
        "/api/v1/analytics/health",
        "/api/v1/calendar/connection/status", 
        "/api/v1/admin/stats"
    )
    
    foreach ($endpoint in $endpoints) {
        try {
            $url = "$BackendUrl$endpoint"
            $response = Invoke-WebRequest -Uri $url -Method Get -TimeoutSec 10 -SkipHttpErrorCheck
            
            if ($response.StatusCode -in @(200, 401, 403)) {
                Add-TestResult -TestName "API Endpoint: $endpoint" -Status "PASS" -Details "Endpoint accessible at standardized URL"
            }
            else {
                Add-TestResult -TestName "API Endpoint: $endpoint" -Status "WARNING" -Details "HTTP $($response.StatusCode) - may require authentication"
            }
        }
        catch {
            Add-TestResult -TestName "API Endpoint: $endpoint" -Status "ERROR" -Details "Exception: $($_.Exception.Message)"
        }
    }
}

function Test-WebhookProcessingFlow {
    Write-Host "🔍 Testing End-to-End Webhook Processing..." -ForegroundColor Yellow
    
    $testCases = @(
        @{ Status = "twilio-approved"; Expected = "success" },
        @{ Status = "twilio-rejected"; Expected = "success" },
        @{ Status = "pending-review"; Expected = "success" }
    )
    
    for ($i = 0; $i -lt $testCases.Count; $i++) {
        $testCase = $testCases[$i]
        
        # Generate valid 34-character CustomerProfileSid (BU + 32 hex chars)
        $testPayload = @{
            CustomerProfileSid = "BU$($i.ToString().PadLeft(2, '0'))$('a' * 30)"
            Status = $testCase.Status
            EventType = "customer-profile.status.changed"
            Timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        } | ConvertTo-Json
        
        try {
            $headers = @{ "Content-Type" = "application/json" }
            $response = Invoke-RestMethod -Uri $WebhookEndpoint -Method Post -Body $testPayload -Headers $headers -TimeoutSec 30
            
            Add-TestResult -TestName "E2E Webhook: $($testCase.Status)" -Status "PASS" -Details "Status $($testCase.Status) processed successfully"
        }
        catch {
            $statusCode = $_.Exception.Response.StatusCode.value__
            $errorMessage = $_.Exception.Message
            Add-TestResult -TestName "E2E Webhook: $($testCase.Status)" -Status "FAIL" -Details "HTTP $($statusCode): $errorMessage"
        }
        
        Start-Sleep -Seconds 1
    }
}

function Show-TestReport {
    Write-Host "`n📊 Generating Test Report..." -ForegroundColor Cyan
    Write-Host "=" * 60 -ForegroundColor Gray
    
    $passed = ($TestResults | Where-Object { $_.Status -like "*PASS*" }).Count
    $failed = ($TestResults | Where-Object { $_.Status -like "*FAIL*" }).Count
    $warnings = ($TestResults | Where-Object { $_.Status -like "*WARNING*" }).Count
    
    Write-Host "`n🎯 WEBHOOK INTEGRATION FIXES - TEST RESULTS" -ForegroundColor White
    Write-Host "=" * 60 -ForegroundColor Gray
    Write-Host "📈 Total Tests: $($TestResults.Count)" -ForegroundColor White
    Write-Host "Passed: $passed" -ForegroundColor Green
    Write-Host "Failed: $failed" -ForegroundColor Red
    Write-Host "Warnings: $warnings" -ForegroundColor Yellow
    Write-Host "=" * 60 -ForegroundColor Gray
    
    foreach ($result in $TestResults) {
        $color = switch -Wildcard ($result.Status) {
            "*PASS*" { "Green" }
            "*FAIL*" { "Red" }
            "*WARNING*" { "Yellow" }
            default { "White" }
        }
        
        Write-Host "`n$($result.Status) $($result.Test)" -ForegroundColor $color
        Write-Host "   📝 $($result.Details)" -ForegroundColor Gray
    }
    
    Write-Host "`n" + "=" * 60 -ForegroundColor Gray
    
    if ($failed -eq 0) {
        Write-Host "ALL CRITICAL FIXES VALIDATED SUCCESSFULLY!" -ForegroundColor Green
        Write-Host "Business Profile Webhook Logging Integration: WORKING" -ForegroundColor Green
        Write-Host "API Base URL Configuration Standardization: WORKING" -ForegroundColor Green
    }
    else {
        Write-Host "SOME TESTS FAILED - REVIEW REQUIRED" -ForegroundColor Yellow
        Write-Host "Please check the failed tests and fix any issues." -ForegroundColor Yellow
    }
    
    Write-Host "=" * 60 -ForegroundColor Gray
}

# Main execution
try {
    # Test backend connectivity first
    if (-not (Test-BackendConnectivity)) {
        Write-Host "Backend server is not accessible. Please start the backend server and try again." -ForegroundColor Red
        exit 1
    }
    
    # Run all tests
    Test-BusinessProfileWebhook
    Test-APIConfigurationStandardization  
    Test-WebhookProcessingFlow
    
    # Generate report
    Show-TestReport
    
    Write-Host "`nTest execution completed!" -ForegroundColor Cyan
}
catch {
    Write-Host "Test execution failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
